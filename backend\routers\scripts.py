from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from database.database import get_db
from models.script import Script

router = APIRouter()

# Pydantic models for request/response
class ScriptBase(BaseModel):
    title: str
    content: str
    category: Optional[str] = None

class ScriptCreate(ScriptBase):
    pass

class ScriptUpdate(ScriptBase):
    pass

class ScriptResponse(ScriptBase):
    id: int
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True

class ScriptListResponse(BaseModel):
    items: List[ScriptResponse]
    total: int

# API endpoints
@router.get("/scripts", response_model=ScriptListResponse)
def get_scripts(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取话术列表"""
    query = db.query(Script)
    
    # 搜索过滤
    if search:
        query = query.filter(
            Script.title.contains(search) | 
            Script.content.contains(search)
        )
    
    # 分类过滤
    if category:
        query = query.filter(Script.category == category)
    
    # 总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    scripts = query.order_by(Script.updated_at.desc()).offset(offset).limit(page_size).all()
    
    return ScriptListResponse(
        items=[ScriptResponse.from_orm(script) for script in scripts],
        total=total
    )

@router.post("/scripts", response_model=ScriptResponse)
def create_script(script: ScriptCreate, db: Session = Depends(get_db)):
    """创建新话术"""
    db_script = Script(**script.dict())
    db.add(db_script)
    db.commit()
    db.refresh(db_script)
    return ScriptResponse.from_orm(db_script)

@router.get("/scripts/{script_id}", response_model=ScriptResponse)
def get_script(script_id: int, db: Session = Depends(get_db)):
    """获取单个话术"""
    script = db.query(Script).filter(Script.id == script_id).first()
    if not script:
        raise HTTPException(status_code=404, detail="话术不存在")
    return ScriptResponse.from_orm(script)

@router.put("/scripts/{script_id}", response_model=ScriptResponse)
def update_script(script_id: int, script: ScriptUpdate, db: Session = Depends(get_db)):
    """更新话术"""
    db_script = db.query(Script).filter(Script.id == script_id).first()
    if not db_script:
        raise HTTPException(status_code=404, detail="话术不存在")
    
    for key, value in script.dict().items():
        setattr(db_script, key, value)
    
    db.commit()
    db.refresh(db_script)
    return ScriptResponse.from_orm(db_script)

@router.delete("/scripts/{script_id}")
def delete_script(script_id: int, db: Session = Depends(get_db)):
    """删除话术"""
    script = db.query(Script).filter(Script.id == script_id).first()
    if not script:
        raise HTTPException(status_code=404, detail="话术不存在")
    
    db.delete(script)
    db.commit()
    return {"message": "话术删除成功"}

@router.post("/scripts/{script_id}/copy", response_model=ScriptResponse)
def copy_script(script_id: int, db: Session = Depends(get_db)):
    """复制话术"""
    original_script = db.query(Script).filter(Script.id == script_id).first()
    if not original_script:
        raise HTTPException(status_code=404, detail="话术不存在")
    
    # 创建副本
    new_script = Script(
        title=f"{original_script.title} (副本)",
        content=original_script.content,
        category=original_script.category
    )
    
    db.add(new_script)
    db.commit()
    db.refresh(new_script)
    return ScriptResponse.from_orm(new_script)

@router.get("/scripts/{script_id}/copy-to-clipboard")
def copy_to_clipboard(script_id: int, db: Session = Depends(get_db)):
    """复制话术内容到剪贴板"""
    script = db.query(Script).filter(Script.id == script_id).first()
    if not script:
        raise HTTPException(status_code=404, detail="话术不存在")
    
    try:
        # 注意：在服务器端复制到剪贴板可能不会工作，这个功能应该在前端实现
        # 这里只是返回内容供前端复制
        return {"content": script.content, "message": "话术内容获取成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"复制失败: {str(e)}")

@router.get("/scripts/categories")
def get_categories(db: Session = Depends(get_db)):
    """获取所有话术分类"""
    categories = db.query(Script.category).distinct().filter(Script.category.isnot(None)).all()
    return {"categories": [cat[0] for cat in categories if cat[0]]}
